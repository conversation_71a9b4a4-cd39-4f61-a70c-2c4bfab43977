/**
 * Admin Route Protection Middleware
 * Protects all /admin/* routes except /admin/login/
 */

/**
 * Parse cookies from request headers
 */
function parseCookies(cookieHeader) {
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

/**
 * Verify session token with embedded data
 */
function verifySession(sessionToken, clientIP) {
  try {
    // Parse the session token
    const [encodedData, signature] = sessionToken.split('.');

    if (!encodedData || !signature) {
      return { valid: false, reason: 'Invalid token format' };
    }

    // Decode session data
    const sessionData = JSON.parse(atob(encodedData));

    if (!sessionData || !sessionData.authenticated) {
      return { valid: false, reason: 'Invalid session data' };
    }

    const now = Date.now();

    // Check if session has expired
    if (now > sessionData.expiresAt) {
      return { valid: false, reason: 'Session expired' };
    }

    // Check IP consistency
    if (sessionData.ip !== clientIP) {
      return { valid: false, reason: 'IP mismatch' };
    }

    return { valid: true };
  } catch (error) {
    console.error('Session verification error:', error);
    return { valid: false, reason: 'Token parsing failed' };
  }
}

/**
 * Middleware function to protect admin routes
 */
export async function onRequest(context) {
  const { request, next } = context;
  const url = new URL(request.url);
  
  // Allow access to login page and auth API endpoints
  if (url.pathname === '/admin/login/' || 
      url.pathname.startsWith('/api/auth/')) {
    return next();
  }
  
  // For all other admin routes, check authentication
  if (url.pathname.startsWith('/admin/')) {
    try {
      // Get client IP
      const clientIP = request.headers.get('CF-Connecting-IP') || 
                      request.headers.get('X-Forwarded-For') || 
                      'unknown';
      
      // Parse cookies
      const cookieHeader = request.headers.get('Cookie');
      const cookies = parseCookies(cookieHeader);
      const sessionToken = cookies.admin_session;
      
      if (!sessionToken) {
        // No session token, redirect to login
        return Response.redirect(new URL('/admin/login/', request.url), 302);
      }
      
      // Verify session
      const verification = verifySession(sessionToken, clientIP);
      
      if (!verification.valid) {
        // Invalid session, redirect to login
        const response = Response.redirect(new URL('/admin/login/', request.url), 302);
        
        // Clear invalid session cookie
        response.headers.set('Set-Cookie', 
          `admin_session=; ` +
          `HttpOnly; ` +
          `Secure; ` +
          `SameSite=Strict; ` +
          `Max-Age=0; ` +
          `Path=/admin; ` +
          `Expires=Thu, 01 Jan 1970 00:00:00 GMT`
        );
        
        return response;
      }
      
      // Session is valid, allow access
      return next();
      
    } catch (error) {
      console.error('Admin middleware error:', error);
      
      // On error, redirect to login
      return Response.redirect(new URL('/admin/login/', request.url), 302);
    }
  }
  
  // For non-admin routes, continue normally
  return next();
}
