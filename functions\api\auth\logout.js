/**
 * Admin Logout Handler
 * Handles secure logout and session cleanup
 */

// Import session store from login function
import { activeSessions } from './login.js';

/**
 * Parse cookies from request headers
 */
function parseCookies(cookieHeader) {
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

/**
 * Main logout handler
 */
export async function onRequestPost(context) {
  const { request } = context;
  
  try {
    // Parse cookies
    const cookieHeader = request.headers.get('Cookie');
    const cookies = parseCookies(cookieHeader);
    const sessionToken = cookies.admin_session;
    
    // Remove session from active sessions if it exists
    if (sessionToken) {
      activeSessions.delete(sessionToken);
    }
    
    // Create response
    const response = new Response(
      JSON.stringify({
        success: true,
        message: 'Logged out successfully'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    // Clear the session cookie
    response.headers.set('Set-Cookie', 
      `admin_session=; ` +
      `HttpOnly; ` +
      `Secure; ` +
      `SameSite=Strict; ` +
      `Max-Age=0; ` +
      `Path=/admin; ` +
      `Expires=Thu, 01 Jan 1970 00:00:00 GMT`
    );
    
    return response;
    
  } catch (error) {
    console.error('Logout error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: 'Logout failed'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle GET requests (also allow logout via GET)
 */
export async function onRequestGet(context) {
  return onRequestPost(context);
}
