---
export const prerender = true;

import Layout from '../layouts/Layout.astro';
import ModernAdminPanel from '../components/ModernAdminPanel.astro';
import '../assets/css/admin.css';
---

<Layout
  title="Admin Panel | Cheers Marketplace"
  description="Administrative panel for managing Cheers Marketplace products and content."
  noIndex={true}
>
    <meta name="description" content="Admin panel for managing products" />
    <meta name="robots" content="noindex, nofollow" />
  </Fragment>

  <main class="admin-main">
    <!-- Authentication Check -->
    <div id="auth-loading" class="auth-loading">
      <div class="loading-content">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="currentColor" class="loading-spinner">
          <path d="M12,4V2A10,10 0 0,0 2,12H4A8,8 0 0,1 12,4Z"/>
        </svg>
        <p>Verifying authentication...</p>
      </div>
    </div>

    <div id="auth-failed" class="auth-failed" style="display: none;">
      <div class="auth-failed-content">
        <svg width="64" height="64" viewBox="0 0 24 24" fill="currentColor">
          <path d="M12,1L3,5V11C3,16.55 6.84,21.74 12,23C17.16,21.74 21,16.55 21,11V5L12,1M12,7C13.4,7 14.8,8.6 14.8,10V11.5C15.4,11.5 16,12.4 16,13V16C16,17.4 15.4,18 14.8,18H9.2C8.6,18 8,17.4 8,16V13C8,12.4 8.6,11.5 9.2,11.5V10C9.2,8.6 10.6,7 12,7M12,8.2C11.2,8.2 10.5,8.7 10.5,10V11.5H13.5V10C13.5,8.7 12.8,8.2 12,8.2Z"/>
        </svg>
        <h2>Authentication Required</h2>
        <p>You must be logged in to access the admin panel.</p>
        <div class="auth-actions">
          <a href="/admin/login/" class="btn-login">Login</a>
          <a href="/" class="btn-home">Back to Home</a>
        </div>
      </div>
    </div>

    <div id="admin-panel" style="display: none;">
      <ModernAdminPanel />

      <!-- Logout Button -->
      <div class="admin-logout">
        <button id="logout-btn" class="btn-logout">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M14,12L10,8V11H2V13H10V16M20,12A8,8 0 0,1 12,20A8,8 0 0,1 4,12A8,8 0 0,1 12,4C12.76,4 13.5,4.11 14.2,4.31L15.77,2.74C14.61,2.26 13.34,2 12,2A10,10 0 0,0 2,12A10,10 0 0,0 12,22A10,10 0 0,0 22,12H20Z"/>
          </svg>
          Logout
        </button>
      </div>
    </div>
  </main>

  <footer>
    <div class="container">
      <p>Admin access only. <span aria-label="lock" role="img">🔒</span></p>
    </div>
  </footer>
</Layout>

<style>
  .admin-main {
    padding-top: 0;
    min-height: auto;
  }

  /* Authentication Loading */
  .auth-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
  }

  .loading-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .loading-spinner {
    color: var(--primary);
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }

  /* Authentication Failed */
  .auth-failed {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
    padding: 2rem;
    background: var(--light-background);
    border-radius: var(--radius-xl);
    border: 1px solid var(--border);
    margin: 2rem auto;
    max-width: 600px;
  }

  .auth-failed-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
  }

  .auth-failed svg {
    color: var(--muted);
    margin-bottom: 1rem;
  }

  .auth-failed h2 {
    color: var(--text);
    margin: 0;
    font-size: 1.75rem;
  }

  .auth-failed p {
    color: var(--text-secondary);
    margin: 0;
    font-size: 1.125rem;
    line-height: 1.6;
  }

  .auth-actions {
    display: flex;
    gap: 1rem;
    margin-top: 1.5rem;
  }

  .btn-login,
  .btn-home {
    padding: 0.75rem 1.5rem;
    border-radius: var(--radius-lg);
    text-decoration: none;
    font-weight: 600;
    transition: all 0.2s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
  }

  .btn-login {
    background: var(--primary);
    color: white;
  }

  .btn-login:hover {
    background: var(--primary-dark);
    transform: translateY(-1px);
  }

  .btn-home {
    background: var(--background);
    color: var(--text-secondary);
    border: 1px solid var(--border);
  }

  .btn-home:hover {
    background: var(--border-light);
    color: var(--text);
  }

  /* Logout Button */
  .admin-logout {
    position: fixed;
    top: 1rem;
    right: 1rem;
    z-index: 1000;
  }

  .btn-logout {
    background: var(--danger);
    color: white;
    border: none;
    padding: 0.75rem 1rem;
    border-radius: var(--radius-lg);
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: var(--shadow);
  }

  .btn-logout:hover {
    background: var(--danger-dark);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
  }

  @media (max-width: 768px) {
    .auth-actions {
      flex-direction: column;
      width: 100%;
    }

    .btn-login,
    .btn-home {
      justify-content: center;
    }

    .admin-logout {
      position: static;
      margin: 1rem;
    }

    .btn-logout {
      width: 100%;
      justify-content: center;
    }
  }
</style>

<script>
  document.addEventListener('DOMContentLoaded', async function() {
    const authLoading = document.getElementById('auth-loading');
    const authFailed = document.getElementById('auth-failed');
    const adminPanel = document.getElementById('admin-panel');
    const logoutBtn = document.getElementById('logout-btn');

    try {
      // First, test what cookies we have
      console.log('Document cookies:', document.cookie);

      // Check authentication
      const response = await fetch('/api/auth/verify', {
        method: 'POST',
        credentials: 'include'
      });

      const result = await response.json();
      console.log('Auth verification response:', result);

      if (response.ok && result.authenticated) {
        // User is authenticated, show admin panel
        console.log('Authentication successful');
        authLoading.style.display = 'none';
        adminPanel.style.display = 'block';
      } else {
        // User is not authenticated, show login prompt
        console.log('Authentication failed:', result);
        authLoading.style.display = 'none';
        authFailed.style.display = 'flex';
      }
    } catch (error) {
      console.error('Authentication check failed:', error);
      // On error, show login prompt
      authLoading.style.display = 'none';
      authFailed.style.display = 'flex';
    }

    // Logout functionality
    if (logoutBtn) {
      logoutBtn.addEventListener('click', async function() {
        try {
          const response = await fetch('/api/auth/logout', {
            method: 'POST',
            credentials: 'include'
          });

          if (response.ok) {
            // Redirect to login page
            window.location.href = '/admin/login/';
          } else {
            alert('Logout failed. Please try again.');
          }
        } catch (error) {
          console.error('Logout error:', error);
          alert('Logout failed. Please try again.');
        }
      });
    }
  });
</script>
