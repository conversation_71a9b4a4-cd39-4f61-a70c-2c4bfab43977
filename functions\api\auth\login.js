/**
 * Secure Admin Authentication for Cheers Marketplace
 * Handles login with password hashing, rate limiting, and session management
 */

// Admin password for verification
const ADMIN_PASSWORD = 'MikeyJenn19!';

// Rate limiting store (in-memory, resets on function restart)
const loginAttempts = new Map();
const RATE_LIMIT_WINDOW = 15 * 60 * 1000; // 15 minutes
const MAX_ATTEMPTS = 5;

// Session store (in-memory, resets on function restart)
const activeSessions = new Map();
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Generate a secure random session token
 */
async function generateSessionToken() {
  const array = new Uint8Array(32);
  crypto.getRandomValues(array);
  return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
}

/**
 * Simple password verification for development
 * In production, you would use proper password hashing
 */
function verifyPassword(password) {
  return password === ADMIN_PASSWORD;
}

/**
 * Check rate limiting
 */
function checkRateLimit(ip) {
  const now = Date.now();
  const attempts = loginAttempts.get(ip) || [];
  
  // Remove old attempts outside the window
  const recentAttempts = attempts.filter(timestamp => now - timestamp < RATE_LIMIT_WINDOW);
  
  if (recentAttempts.length >= MAX_ATTEMPTS) {
    return false;
  }
  
  // Add current attempt
  recentAttempts.push(now);
  loginAttempts.set(ip, recentAttempts);
  
  return true;
}

/**
 * Create secure session
 */
async function createSession(ip) {
  const sessionToken = await generateSessionToken();
  const expiresAt = Date.now() + SESSION_DURATION;
  
  activeSessions.set(sessionToken, {
    ip,
    createdAt: Date.now(),
    expiresAt,
    lastActivity: Date.now()
  });
  
  return sessionToken;
}

/**
 * Validate input
 */
function validateInput(password) {
  if (!password || typeof password !== 'string') {
    return { valid: false, error: 'Password is required' };
  }
  
  if (password.length < 8 || password.length > 128) {
    return { valid: false, error: 'Invalid password format' };
  }
  
  return { valid: true };
}

/**
 * Main login handler
 */
export async function onRequestPost(context) {
  const { request } = context;
  
  try {
    // Get client IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    'unknown';
    
    // Check rate limiting
    if (!checkRateLimit(clientIP)) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Too many login attempts. Please wait 15 minutes before trying again.',
          rateLimited: true
        }),
        {
          status: 429,
          headers: { 
            'Content-Type': 'application/json',
            'X-RateLimit-Limit': MAX_ATTEMPTS.toString(),
            'X-RateLimit-Window': (RATE_LIMIT_WINDOW / 1000).toString()
          }
        }
      );
    }
    
    // Parse form data
    const formData = await request.formData();
    const password = formData.get('password');
    
    // Validate input
    const validation = validateInput(password);
    if (!validation.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          error: validation.error
        }),
        {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Verify password
    console.log('Attempting login with password length:', password.length);
    const isValidPassword = verifyPassword(password);
    console.log('Password verification result:', isValidPassword);

    if (!isValidPassword) {
      return new Response(
        JSON.stringify({
          success: false,
          error: 'Invalid password. Please try again.'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Create session
    const sessionToken = await createSession(clientIP);
    
    // Set secure cookie
    const response = new Response(
      JSON.stringify({
        success: true,
        message: 'Login successful'
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
    // Set secure HTTP-only cookie
    response.headers.set('Set-Cookie', 
      `admin_session=${sessionToken}; ` +
      `HttpOnly; ` +
      `Secure; ` +
      `SameSite=Strict; ` +
      `Max-Age=${SESSION_DURATION / 1000}; ` +
      `Path=/admin`
    );
    
    return response;
    
  } catch (error) {
    console.error('Login error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        error: 'An unexpected error occurred. Please try again.'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle GET requests (redirect to login page)
 */
export async function onRequestGet(context) {
  return new Response(
    JSON.stringify({
      success: false,
      error: 'This endpoint only accepts POST requests.',
      method: 'GET'
    }),
    {
      status: 405,
      headers: { 'Content-Type': 'application/json' }
    }
  );
}

// Export session validation function for use by other functions
export { activeSessions, SESSION_DURATION };
