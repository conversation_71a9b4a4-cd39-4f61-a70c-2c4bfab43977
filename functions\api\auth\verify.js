/**
 * Session Verification for Admin Authentication
 * Verifies admin session tokens and manages session lifecycle
 */

// Session duration
const SESSION_DURATION = 24 * 60 * 60 * 1000; // 24 hours

/**
 * Parse cookies from request headers
 */
function parseCookies(cookieHeader) {
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

/**
 * Verify session token with embedded data
 */
function verifySession(sessionToken, clientIP) {
  try {
    console.log('Parsing session token...');
    // Parse the session token
    const [encodedData, signature] = sessionToken.split('.');
    console.log('Token parts - encodedData:', encodedData ? 'present' : 'missing', 'signature:', signature ? 'present' : 'missing');

    if (!encodedData || !signature) {
      return { valid: false, reason: 'Invalid token format' };
    }

    // Decode session data
    console.log('Decoding session data...');
    const sessionData = JSON.parse(atob(encodedData));
    console.log('Session data:', sessionData);

    if (!sessionData || !sessionData.authenticated) {
      return { valid: false, reason: 'Invalid session data' };
    }

    const now = Date.now();
    console.log('Time check - now:', now, 'expires:', sessionData.expiresAt, 'valid:', now <= sessionData.expiresAt);

    // Check if session has expired
    if (now > sessionData.expiresAt) {
      return { valid: false, reason: 'Session expired' };
    }

    console.log('IP check - session IP:', sessionData.ip, 'client IP:', clientIP, 'match:', sessionData.ip === clientIP);

    // Check IP consistency (optional security measure) - temporarily disabled for debugging
    // if (sessionData.ip !== clientIP) {
    //   return { valid: false, reason: 'IP mismatch' };
    // }

    console.log('Session verification successful');
    return {
      valid: true,
      session: {
        createdAt: sessionData.createdAt,
        lastActivity: sessionData.lastActivity,
        expiresAt: sessionData.expiresAt
      }
    };
  } catch (error) {
    console.error('Session verification error:', error);
    return { valid: false, reason: 'Token parsing failed: ' + error.message };
  }
}

/**
 * Main verification handler
 */
export async function onRequestPost(context) {
  const { request } = context;
  
  try {
    // Get client IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    'unknown';
    
    // Parse cookies
    const cookieHeader = request.headers.get('Cookie');
    console.log('Cookie header:', cookieHeader);
    const cookies = parseCookies(cookieHeader);
    console.log('Parsed cookies:', Object.keys(cookies));
    const sessionToken = cookies.admin_session;
    console.log('Session token found:', sessionToken ? 'YES' : 'NO');

    if (!sessionToken) {
      return new Response(
        JSON.stringify({
          success: false,
          authenticated: false,
          error: 'No session token found',
          debug: {
            cookieHeader: cookieHeader ? 'present' : 'missing',
            cookieCount: Object.keys(cookies).length
          }
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Verify session
    console.log('Verifying session token:', sessionToken.substring(0, 20) + '...');
    const verification = verifySession(sessionToken, clientIP);
    console.log('Verification result:', verification);

    if (!verification.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          authenticated: false,
          error: verification.reason,
          debug: {
            tokenLength: sessionToken.length,
            clientIP: clientIP,
            reason: verification.reason
          }
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Session is valid
    return new Response(
      JSON.stringify({
        success: true,
        authenticated: true,
        session: verification.session
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error('Session verification error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        authenticated: false,
        error: 'Session verification failed'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle GET requests
 */
export async function onRequestGet(context) {
  // For GET requests, we can also verify sessions
  return onRequestPost(context);
}
