/**
 * Session Verification for Admin Authentication
 * Verifies admin session tokens and manages session lifecycle
 */

// Import session store from login function
import { activeSessions, SESSION_DURATION } from './login.js';

/**
 * Parse cookies from request headers
 */
function parseCookies(cookieHeader) {
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  return cookies;
}

/**
 * Verify session token
 */
function verifySession(sessionToken, clientIP) {
  const session = activeSessions.get(sessionToken);
  
  if (!session) {
    return { valid: false, reason: 'Session not found' };
  }
  
  const now = Date.now();
  
  // Check if session has expired
  if (now > session.expiresAt) {
    activeSessions.delete(sessionToken);
    return { valid: false, reason: 'Session expired' };
  }
  
  // Check IP consistency (optional security measure)
  if (session.ip !== clientIP) {
    activeSessions.delete(sessionToken);
    return { valid: false, reason: 'IP mismatch' };
  }
  
  // Update last activity
  session.lastActivity = now;
  activeSessions.set(sessionToken, session);
  
  return { 
    valid: true, 
    session: {
      createdAt: session.createdAt,
      lastActivity: session.lastActivity,
      expiresAt: session.expiresAt
    }
  };
}

/**
 * Clean up expired sessions
 */
function cleanupExpiredSessions() {
  const now = Date.now();
  for (const [token, session] of activeSessions.entries()) {
    if (now > session.expiresAt) {
      activeSessions.delete(token);
    }
  }
}

/**
 * Main verification handler
 */
export async function onRequestPost(context) {
  const { request } = context;
  
  try {
    // Clean up expired sessions periodically
    cleanupExpiredSessions();
    
    // Get client IP
    const clientIP = request.headers.get('CF-Connecting-IP') || 
                    request.headers.get('X-Forwarded-For') || 
                    'unknown';
    
    // Parse cookies
    const cookieHeader = request.headers.get('Cookie');
    const cookies = parseCookies(cookieHeader);
    const sessionToken = cookies.admin_session;
    
    if (!sessionToken) {
      return new Response(
        JSON.stringify({
          success: false,
          authenticated: false,
          error: 'No session token found'
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Verify session
    const verification = verifySession(sessionToken, clientIP);
    
    if (!verification.valid) {
      return new Response(
        JSON.stringify({
          success: false,
          authenticated: false,
          error: verification.reason
        }),
        {
          status: 401,
          headers: { 'Content-Type': 'application/json' }
        }
      );
    }
    
    // Session is valid
    return new Response(
      JSON.stringify({
        success: true,
        authenticated: true,
        session: verification.session
      }),
      {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      }
    );
    
  } catch (error) {
    console.error('Session verification error:', error);
    
    return new Response(
      JSON.stringify({
        success: false,
        authenticated: false,
        error: 'Session verification failed'
      }),
      {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      }
    );
  }
}

/**
 * Handle GET requests
 */
export async function onRequestGet(context) {
  // For GET requests, we can also verify sessions
  return onRequestPost(context);
}
