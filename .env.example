# Snipcart Configuration
# Get your API key from: https://app.snipcart.com/dashboard/account/credentials
# Use your TEST API key for development and LIVE API key for production
PUBLIC_SNIPCART_API_KEY=YTY0ZTIxNjYtNDVlMC00NmUyLWFkNjItYTg3ZmNhMTc4MDQyNjM4ODczNzI4NTM0MTY0NDA4

# Snipcart Webhook Secret (for validating webhook requests)
# Get this from: https://app.snipcart.com/dashboard/webhooks
SNIPCART_WEBHOOK_SECRET=your_webhook_secret_here

# Google Analytics (optional)
PUBLIC_GA_MEASUREMENT_ID=G-XXXXXXXXXX

# GitHub Configuration (Required for admin panel sync)
GITHUB_TOKEN=your_github_personal_access_token
GITHUB_OWNER=your_github_username
GITHUB_REPO=your_repository_name

# Cloudflare Configuration (Optional - for cache purging)
CLOUDFLARE_ZONE_ID=your_cloudflare_zone_id
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token

# Build Hook (Optional - for automatic deployments)
CLOUDFLARE_BUILD_HOOK_URL=your_cloudflare_pages_build_hook_url

# Bunny Storage Configuration (Required for image uploads)
# Get these from your Bunny Storage zone settings
BUNNY_STORAGE_ZONE_NAME=your_storage_zone_name
BUNNY_STORAGE_API_KEY=your_storage_zone_password
BUNNY_STORAGE_REGION=your_storage_region
BUNNY_STORAGE_BASE_URL=https://your-cdn-url.b-cdn.net

# Email Service Configuration (Required for contact form)
# Choose ONE of the following email services:

# Option 1: Resend (Recommended - Simple and reliable)
RESEND_API_KEY=your_resend_api_key

# Option 2: SendGrid
SENDGRID_API_KEY=your_sendgrid_api_key

# Option 3: Cloudflare Email Workers (Advanced)
CLOUDFLARE_EMAIL_API_TOKEN=your_cloudflare_email_api_token
