/**
 * Test endpoint to debug cookie and session issues
 */

export async function onRequestGet(context) {
  const { request } = context;
  
  // Get all headers
  const headers = {};
  for (const [key, value] of request.headers.entries()) {
    headers[key] = value;
  }
  
  // Parse cookies
  const cookieHeader = request.headers.get('Cookie');
  const cookies = {};
  if (cookieHeader) {
    cookieHeader.split(';').forEach(cookie => {
      const [name, value] = cookie.trim().split('=');
      if (name && value) {
        cookies[name] = decodeURIComponent(value);
      }
    });
  }
  
  return new Response(
    JSON.stringify({
      success: true,
      debug: {
        url: request.url,
        method: request.method,
        cookieHeader: cookieHeader || 'No cookies',
        cookieCount: Object.keys(cookies).length,
        cookies: Object.keys(cookies),
        hasAdminSession: !!cookies.admin_session,
        adminSessionLength: cookies.admin_session ? cookies.admin_session.length : 0,
        userAgent: headers['user-agent'] || 'Unknown',
        referer: headers['referer'] || 'None'
      }
    }, null, 2),
    {
      status: 200,
      headers: { 
        'Content-Type': 'application/json',
        'Access-Control-Allow-Origin': '*'
      }
    }
  );
}

export async function onRequestPost(context) {
  return onRequestGet(context);
}
